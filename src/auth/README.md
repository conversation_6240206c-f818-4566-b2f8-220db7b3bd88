# OAuth Authentication System

## Overview

This directory contains a comprehensive OAuth authentication system for the Orbitum application, featuring enhanced error handling, auto-restart capabilities, and robust frontend integration.

## Recent Improvements

### ✅ Enhanced Error Handling System
- **Comprehensive Error Boundaries**: Prevents application crashes from OAuth-related errors
- **Graceful Fallbacks**: Provides fallback pages when primary error handling fails
- **Rate Limiting**: Prevents abuse with IP-based error rate limiting
- **Context Logging**: Detailed error logging with user context and request information

### ✅ Auto-restart Configuration
- **OAuth Error Detection**: Automatically detects OAuth-related errors in uncaught exceptions
- **Port Cleanup**: Kills existing processes on the port before restarting
- **Restart Limits**: Prevents infinite restart loops with configurable limits
- **Cross-platform Support**: Works on Windows, macOS, and Linux

### ✅ Fixed Timer Redirect URL Bug
- **Proper URL Construction**: Timer now correctly redirects to `${FRONTEND_URL}/auth/google/success`
- **Enhanced JavaScript**: Improved error handling in countdown timer logic
- **Fallback Mechanisms**: Multiple fallback options when primary redirect fails

### ✅ Improved Code Quality
- **TypeScript Typing**: Comprehensive type definitions for all OAuth components
- **Separation of Concerns**: Clear separation between timer logic and redirect logic
- **Clean Code Patterns**: Consistent error handling and validation patterns
- **Service Architecture**: Modular services for different OAuth responsibilities

### ✅ Frontend Page Validation
- **Page Existence Checks**: Validates if frontend pages exist before redirecting
- **Accessibility Testing**: HTTP requests to verify page accessibility
- **Graceful Fallbacks**: Automatic fallback to home page when target pages are unavailable
- **Caching**: Intelligent caching of validation results to improve performance

## Architecture

### Core Services

#### 1. OAuthErrorHandlerService
```typescript
// Enhanced error handling with context
await this.oauthErrorHandler.handleOAuthError(
  res,
  OAuthErrorType.AUTHENTICATION_FAILED,
  'Authentication failed',
  'Invalid OAuth response',
  { userId: 'user123', ipAddress: '***********' }
);
```

#### 2. OAuthRedirectManagerService
```typescript
// Comprehensive redirect script generation
const script = this.redirectManager.generateRedirectScript(
  OAuthRedirectType.SUCCESS,
  successData,
  { autoRedirect: true, redirectDelay: 3000 },
  frontendUrl
);
```

#### 3. FrontendValidationService
```typescript
// Validate frontend pages before redirecting
const validation = await this.frontendValidationService.validateFrontendPage(
  'http://localhost:3000/auth/google/success',
  { timeout: 5000, retries: 2, checkAccessibility: true }
);
```

#### 4. AutoRestartService
```typescript
// Restart application with port cleanup
await this.autoRestartService.restartApplication({
  reason: 'OAuth authentication error',
  killExistingProcess: true,
  delay: 2000
});
```

### OAuth Flow

#### Success Flow
1. User completes Google OAuth
2. Callback handler validates user data
3. Frontend page validation ensures target page exists
4. HTML response generated with enhanced redirect script
5. Auto-redirect with countdown timer to `/auth/google/success`
6. Manual redirect button as fallback

#### Error Flow
1. OAuth error detected
2. Error context collected (IP, user agent, etc.)
3. Rate limiting check performed
4. Frontend error page validated
5. Comprehensive error page generated
6. Fallback mechanisms activated if needed

#### Auto-restart Flow
1. Uncaught exception/rejection detected
2. OAuth-related error keywords checked
3. Port cleanup performed
4. Application restart initiated
5. Restart limits enforced

## Configuration

### Environment Variables
```bash
FRONTEND_URL=http://localhost:3000
PORT=3001
MAX_RESTARTS=5
NODE_ENV=development
```

### Redirect URLs
- **Success**: `${FRONTEND_URL}/auth/google/success`
- **Account Linking**: `${FRONTEND_URL}/profile/linked`
- **Error**: `${FRONTEND_URL}/auth/error`

## Security Features

### 1. URL Validation
- Origin validation against FRONTEND_URL
- Protocol validation (HTTP/HTTPS only)
- XSS prevention through proper escaping

### 2. Content Security Policy
- Strict CSP headers applied to all OAuth responses
- Inline script restrictions with nonce support
- External resource loading controls

### 3. Rate Limiting
- IP-based error rate limiting (10 errors per minute)
- Automatic cleanup of rate limit counters
- Graceful handling of rate-limited requests

### 4. Error Boundaries
- Multiple layers of error handling
- Fallback pages for critical failures
- Comprehensive logging for security monitoring

## Error Types

```typescript
enum OAuthErrorType {
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  INVALID_USER_DATA = 'INVALID_USER_DATA',
  ACCOUNT_LINKING_CONFLICT = 'ACCOUNT_LINKING_CONFLICT',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  INVALID_OAUTH_DATA = 'INVALID_OAUTH_DATA',
  ACCOUNT_INACTIVE = 'ACCOUNT_INACTIVE',
}
```

## Frontend Integration

### Popup Flow
```javascript
// PostMessage API for popup communication
window.opener.postMessage({
  type: 'OAUTH_SUCCESS',
  data: oauthData,
  success: true,
  redirectUrl: 'http://localhost:3000/auth/google/success'
}, frontendBaseUrl);
```

### Redirect Flow
```javascript
// Enhanced redirect with error handling
function redirectToFrontend(url) {
  try {
    if (!url || typeof url !== 'string') {
      url = frontendBaseUrl;
    }
    window.location.href = url;
  } catch (error) {
    console.error('Redirect error:', error);
    window.location.href = frontendBaseUrl;
  }
}
```

## Testing

### Manual Testing
1. Start the application: `npm run start:dev`
2. Navigate to: `http://localhost:3001/api/v1/auth/google`
3. Complete OAuth flow
4. Verify redirect to `/auth/google/success`

### Error Testing
1. Modify FRONTEND_URL to invalid URL
2. Trigger OAuth flow
3. Verify fallback error handling
4. Check application logs for error context

### Auto-restart Testing
1. Simulate OAuth error in callback handler
2. Verify application restart
3. Check port cleanup functionality
4. Confirm restart limits enforcement

## Monitoring

### Logs
- OAuth errors with full context
- Frontend validation results
- Auto-restart events
- Security violations

### Metrics
- Error rates by type
- Frontend validation success rates
- Restart frequency
- Response times

## Troubleshooting

### Common Issues

1. **Timer not redirecting**: Check FRONTEND_URL environment variable
2. **Frontend page not found**: Verify target page exists and is accessible
3. **Application not restarting**: Check restart limits and error detection
4. **Security headers blocking**: Review CSP configuration

### Debug Mode
Set `NODE_ENV=development` for verbose logging and additional debug information.

## Future Enhancements

- [ ] OAuth provider abstraction for multiple providers
- [ ] Advanced rate limiting with Redis
- [ ] Metrics dashboard for monitoring
- [ ] A/B testing for redirect flows
- [ ] Mobile app deep linking support
