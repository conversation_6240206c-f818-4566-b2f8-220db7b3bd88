import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as https from 'https';
import * as http from 'http';

export interface FrontendPageValidationResult {
  isValid: boolean;
  exists: boolean;
  accessible: boolean;
  responseTime?: number;
  statusCode?: number;
  error?: string;
  fallbackUrl?: string;
}

export interface ValidationOptions {
  timeout: number;
  retries: number;
  checkAccessibility: boolean;
  useFallback: boolean;
}

@Injectable()
export class FrontendValidationService {
  private readonly logger = new Logger(FrontendValidationService.name);
  private readonly validationCache = new Map<
    string,
    FrontendPageValidationResult
  >();
  private readonly cacheTimeout = 300000; // 5 minutes

  constructor(private readonly configService: ConfigService) {}

  /**
   * Validate if a frontend page exists and is accessible
   */
  async validateFrontendPage(
    url: string,
    options: Partial<ValidationOptions> = {},
  ): Promise<FrontendPageValidationResult> {
    const validationOptions: ValidationOptions = {
      timeout: 5000,
      retries: 2,
      checkAccessibility: true,
      useFallback: true,
      ...options,
    };

    try {
      // Check cache first
      const cached = this.getCachedResult(url);
      if (cached) {
        this.logger.debug(`Using cached validation result for ${url}`);
        return cached;
      }

      // Perform validation
      const result = await this.performValidation(url, validationOptions);

      // Cache the result
      this.cacheResult(url, result);

      return result;
    } catch (error) {
      this.logger.error(`Frontend page validation failed for ${url}:`, error);
      return this.createFailureResult(
        url,
        error.message,
        validationOptions.useFallback,
      );
    }
  }

  /**
   * Validate multiple frontend pages concurrently
   */
  async validateMultiplePages(
    urls: string[],
    options: Partial<ValidationOptions> = {},
  ): Promise<Map<string, FrontendPageValidationResult>> {
    const results = new Map<string, FrontendPageValidationResult>();

    const validationPromises = urls.map(async (url) => {
      const result = await this.validateFrontendPage(url, options);
      results.set(url, result);
      return { url, result };
    });

    try {
      await Promise.allSettled(validationPromises);
    } catch (error) {
      this.logger.error('Error validating multiple pages:', error);
    }

    return results;
  }

  /**
   * Get the best available redirect URL with fallbacks
   */
  async getBestRedirectUrl(
    primaryUrl: string,
    fallbackUrls: string[] = [],
    options: Partial<ValidationOptions> = {},
  ): Promise<string> {
    // Try primary URL first
    const primaryResult = await this.validateFrontendPage(primaryUrl, options);
    if (primaryResult.isValid && primaryResult.accessible) {
      return primaryUrl;
    }

    // Try fallback URLs
    for (const fallbackUrl of fallbackUrls) {
      const fallbackResult = await this.validateFrontendPage(
        fallbackUrl,
        options,
      );
      if (fallbackResult.isValid && fallbackResult.accessible) {
        this.logger.warn(
          `Using fallback URL ${fallbackUrl} instead of ${primaryUrl}`,
        );
        return fallbackUrl;
      }
    }

    // Return default fallback
    const defaultFallback = this.getDefaultFallbackUrl();
    this.logger.error(
      `All URLs failed validation, using default fallback: ${defaultFallback}`,
    );
    return defaultFallback;
  }

  /**
   * Perform the actual validation
   */
  private async performValidation(
    url: string,
    options: ValidationOptions,
  ): Promise<FrontendPageValidationResult> {
    const startTime = Date.now();

    try {
      // Basic URL validation
      const urlValidation = this.validateUrlFormat(url);
      if (!urlValidation.isValid) {
        return this.createFailureResult(
          url,
          urlValidation.error,
          options.useFallback,
        );
      }

      // Check accessibility if requested
      if (options.checkAccessibility) {
        const accessibilityResult = await this.checkAccessibility(url, options);
        const responseTime = Date.now() - startTime;

        return {
          isValid: true,
          exists: accessibilityResult.exists,
          accessible: accessibilityResult.accessible,
          responseTime,
          statusCode: accessibilityResult.statusCode,
          error: accessibilityResult.error,
          fallbackUrl: options.useFallback
            ? this.getDefaultFallbackUrl()
            : undefined,
        };
      }

      // If not checking accessibility, assume it's valid
      return {
        isValid: true,
        exists: true,
        accessible: true,
        responseTime: Date.now() - startTime,
        fallbackUrl: options.useFallback
          ? this.getDefaultFallbackUrl()
          : undefined,
      };
    } catch (error) {
      return this.createFailureResult(url, error.message, options.useFallback);
    }
  }

  /**
   * Check if URL is accessible via HTTP request
   */
  private async checkAccessibility(
    url: string,
    options: ValidationOptions,
  ): Promise<{
    exists: boolean;
    accessible: boolean;
    statusCode?: number;
    error?: string;
  }> {
    let lastError: string | undefined;

    for (let attempt = 0; attempt <= options.retries; attempt++) {
      try {
        const result = await this.makeHttpRequest(url, options.timeout);

        const statusCode = result.statusCode;
        const exists = statusCode < 500;
        const accessible = statusCode >= 200 && statusCode < 400;

        return { exists, accessible, statusCode };
      } catch (error) {
        lastError = error.message;

        if (attempt < options.retries) {
          // Wait before retry
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * (attempt + 1)),
          );
        }
      }
    }

    return {
      exists: false,
      accessible: false,
      error: lastError,
    };
  }

  /**
   * Make HTTP request using Node.js built-in modules
   */
  private makeHttpRequest(
    url: string,
    timeoutMs: number,
  ): Promise<{ statusCode: number }> {
    return new Promise((resolve, reject) => {
      const parsedUrl = new URL(url);
      const isHttps = parsedUrl.protocol === 'https:';
      const httpModule = isHttps ? https : http;

      const options = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (isHttps ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: 'HEAD',
        timeout: timeoutMs,
        headers: {
          'User-Agent': 'OAuth-Redirect-Validator/1.0',
        },
      };

      const req = httpModule.request(options, (res) => {
        resolve({ statusCode: res.statusCode || 0 });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }

  /**
   * Validate URL format
   */
  private validateUrlFormat(url: string): { isValid: boolean; error?: string } {
    try {
      const parsedUrl = new URL(url);

      // Check if it's HTTP/HTTPS
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return { isValid: false, error: 'URL must use HTTP or HTTPS protocol' };
      }

      // Check if hostname is valid
      if (!parsedUrl.hostname) {
        return { isValid: false, error: 'URL must have a valid hostname' };
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: `Invalid URL format: ${error.message}` };
    }
  }

  /**
   * Create a failure result
   */
  private createFailureResult(
    url: string,
    error: string,
    useFallback: boolean,
  ): FrontendPageValidationResult {
    return {
      isValid: false,
      exists: false,
      accessible: false,
      error,
      fallbackUrl: useFallback ? this.getDefaultFallbackUrl() : undefined,
    };
  }

  /**
   * Get cached validation result
   */
  private getCachedResult(url: string): FrontendPageValidationResult | null {
    const cached = this.validationCache.get(url);
    if (cached) {
      // Check if cache is still valid (simple time-based expiration)
      const cacheKey = `${url}_timestamp`;
      const timestamp = this.validationCache.get(cacheKey);
      if (timestamp && Date.now() - (timestamp as any) < this.cacheTimeout) {
        return cached;
      } else {
        // Remove expired cache
        this.validationCache.delete(url);
        this.validationCache.delete(cacheKey);
      }
    }
    return null;
  }

  /**
   * Cache validation result
   */
  private cacheResult(url: string, result: FrontendPageValidationResult): void {
    this.validationCache.set(url, result);
    this.validationCache.set(`${url}_timestamp`, Date.now());
  }

  /**
   * Get default fallback URL
   */
  private getDefaultFallbackUrl(): string {
    return this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3000',
    );
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.validationCache.clear();
    this.logger.log('Frontend validation cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    const keys = Array.from(this.validationCache.keys()).filter(
      (key) => !key.endsWith('_timestamp'),
    );
    return {
      size: keys.length,
      keys,
    };
  }
}
