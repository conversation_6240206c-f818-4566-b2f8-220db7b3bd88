import {
  Controller,
  Post,
  Get,
  Body,
  Request,
  HttpCode,
  HttpStatus,
  UseGuards,
  UseInterceptors,
  Req,
  Re<PERSON>,
  Logger,
} from '@nestjs/common';

import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ClassSerializerInterceptor } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { UserResponseDto } from '../users/dto/user-response.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { OAuthHtmlTemplateService } from './services/oauth-html-template.service';
import { OAuthSecurityService } from './services/oauth-security.service';
import { OAuthErrorHandlerService } from './services/oauth-error-handler.service';
import {
  OAuthSuccessData,
  OAuthAccountLinkingData,
  OAuthErrorType,
} from './dto/oauth-redirect.dto';

@ApiTags('Authentication')
@Controller('auth')
@UseInterceptors(ClassSerializerInterceptor)
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly usersService: UsersService,
    private readonly oauthHtmlTemplateService: OAuthHtmlTemplateService,
    private readonly oauthSecurityService: OAuthSecurityService,
    private readonly oauthErrorHandler: OAuthErrorHandlerService,
  ) {
    // Validate environment on startup
    this.oauthSecurityService.logEnvironmentValidation();
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description: 'Authenticate user with email/username and password',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: LoginResponseDto,
    description: 'Login successful',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials',
  })
  async login(@Body() loginDto: LoginDto): Promise<LoginResponseDto> {
    return this.authService.login(loginDto);
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get user profile',
    description: 'Get current authenticated user profile',
  })
  async getProfile(@Request() req: any): Promise<UserResponseDto> {
    const user = await this.usersService.findByEmail(req.user.email);
    return new UserResponseDto({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      type: user.type as any,
      businessType: user.businessType as any,
      status: user.status as any,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      password: user.password,
    });
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({
    summary: 'Initiate Google OAuth login',
    description: 'Redirects user to Google OAuth consent screen',
  })
  @ApiResponse({
    status: HttpStatus.FOUND,
    description: 'Redirects to Google OAuth',
  })
  async googleAuth() {
    // This method initiates the OAuth flow
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({
    summary: 'Google OAuth callback',
    description:
      'Handles the callback from Google OAuth and redirects to frontend with HTML response',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'OAuth login successful - returns HTML redirect page',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'OAuth authentication failed - returns HTML error page',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description:
      'Internal server error during OAuth process - returns HTML error page',
  })
  async googleAuthRedirect(@Req() req: any, @Res() res: any) {
    try {
      const { user } = req;

      // Handle missing user data
      if (!user) {
        this.logger.error('OAuth callback: No user data received');
        return this.handleOAuthError(
          res,
          OAuthErrorType.AUTHENTICATION_FAILED,
          'OAuth authentication failed - no user data received',
          'No user data received from OAuth provider',
        );
      }

      // Validate user data completeness
      if (!user.id || !user.email) {
        this.logger.error('OAuth callback: Incomplete user data', {
          userId: user.id,
          hasEmail: !!user.email,
        });
        return this.handleOAuthError(
          res,
          OAuthErrorType.INVALID_USER_DATA,
          'OAuth authentication failed - incomplete user data',
          'Missing required user information from OAuth provider',
        );
      }

      // Check if user account is active
      if (user.status !== 'ACTIVE') {
        this.logger.warn('OAuth callback: User account not active', {
          userId: user.id,
          status: user.status,
        });
        return this.handleOAuthError(
          res,
          OAuthErrorType.ACCOUNT_INACTIVE,
          'Your account is not active. Please contact support.',
          `Account status: ${user.status}`,
        );
      }

      // Generate JWT token and user response
      const payload = this.authService.createJwtPayload(user);
      const accessToken = this.authService.generateAccessToken(payload);
      const userResponse = this.authService.createUserResponse(user);

      // Determine if this is a new user or account linking scenario
      const isNewUser =
        user.createdAt &&
        new Date(user.createdAt).getTime() > Date.now() - 60000; // Created within last minute
      const isAccountLinking =
        user.oauthProvider && user.googleId && !isNewUser;

      if (isAccountLinking) {
        return this.handleAccountLinking(res, user, accessToken, userResponse);
      } else {
        return this.handleSuccessfulAuth(
          res,
          accessToken,
          userResponse,
          isNewUser,
        );
      }
    } catch (error) {
      this.logger.error('OAuth callback error:', error);

      // Determine error type and handle appropriately
      if (error.message?.includes('Unauthorized') || error.status === 401) {
        return this.handleOAuthError(
          res,
          OAuthErrorType.AUTHENTICATION_FAILED,
          'OAuth authentication failed',
          error.message,
        );
      }

      if (error.message?.includes('already linked')) {
        return this.handleOAuthError(
          res,
          OAuthErrorType.ACCOUNT_LINKING_CONFLICT,
          'Account is already linked to another OAuth provider',
          error.message,
        );
      }

      return this.handleOAuthError(
        res,
        OAuthErrorType.INTERNAL_SERVER_ERROR,
        'OAuth authentication failed due to server error',
        error.message,
      );
    }
  }

  /**
   * Handle successful OAuth authentication
   */
  private handleSuccessfulAuth(
    res: any,
    accessToken: string,
    userResponse: UserResponseDto,
    isNewUser: boolean,
  ) {
    try {
      const redirectUrl = `${process.env.FRONTEND_URL}/auth/google/success`;

      // Validate redirect URL for security
      const urlValidation =
        this.oauthSecurityService.validateRedirectUrl(redirectUrl);
      if (!urlValidation.isValid) {
        this.logger.error('Invalid redirect URL:', urlValidation.error);
        return this.handleOAuthError(
          res,
          OAuthErrorType.INTERNAL_SERVER_ERROR,
          'Invalid redirect configuration',
          urlValidation.error,
        );
      }

      const successData = new OAuthSuccessData({
        accessToken,
        expiresIn: this.authService['TOKEN_EXPIRATION_SECONDS'],
        user: userResponse,
        redirectUrl,
        isNewUser,
        message: isNewUser
          ? 'Welcome to Orbitum! Your account has been created successfully.'
          : 'Authentication successful! Welcome back.',
      });

      const htmlResponse =
        this.oauthHtmlTemplateService.generateSuccessPage(successData);

      // Apply security headers
      this.oauthSecurityService.applySecurityHeaders(res);

      return res.send(htmlResponse);
    } catch (error) {
      this.logger.error('Error generating success page:', error);
      return this.handleOAuthError(
        res,
        OAuthErrorType.INTERNAL_SERVER_ERROR,
        'Failed to generate success page',
        error.message,
      );
    }
  }

  /**
   * Handle account linking scenario
   */
  private handleAccountLinking(
    res: any,
    _user: any,
    accessToken: string,
    userResponse: UserResponseDto,
  ) {
    try {
      const redirectUrl = `${process.env.FRONTEND_URL}/profile/linked`;

      // Validate redirect URL for security
      const urlValidation =
        this.oauthSecurityService.validateRedirectUrl(redirectUrl);
      if (!urlValidation.isValid) {
        this.logger.error('Invalid redirect URL:', urlValidation.error);
        return this.handleOAuthError(
          res,
          OAuthErrorType.INTERNAL_SERVER_ERROR,
          'Invalid redirect configuration',
          urlValidation.error,
        );
      }

      const linkingData = new OAuthAccountLinkingData({
        existingUser: userResponse,
        provider: 'google',
        message:
          'Your Google account has been successfully linked to your existing account.',
        redirectUrl,
        accessToken,
        expiresIn: this.authService['TOKEN_EXPIRATION_SECONDS'],
      });

      const htmlResponse =
        this.oauthHtmlTemplateService.generateAccountLinkingPage(linkingData);

      // Apply security headers
      this.oauthSecurityService.applySecurityHeaders(res);

      return res.send(htmlResponse);
    } catch (error) {
      this.logger.error('Error generating account linking page:', error);
      return this.handleOAuthError(
        res,
        OAuthErrorType.INTERNAL_SERVER_ERROR,
        'Failed to generate account linking page',
        error.message,
      );
    }
  }

  /**
   * Handle OAuth errors with enhanced error handling
   */
  private async handleOAuthError(
    res: any,
    errorType: OAuthErrorType,
    message: string,
    details?: string,
  ) {
    // Extract context information for better error handling
    const context = {
      timestamp: new Date().toISOString(),
      userAgent: res.req?.headers?.['user-agent'],
      ipAddress: res.req?.ip || res.req?.connection?.remoteAddress,
    };

    // Use the enhanced error handler
    return this.oauthErrorHandler.handleOAuthError(
      res,
      errorType,
      message,
      details,
      context,
    );
  }
}
