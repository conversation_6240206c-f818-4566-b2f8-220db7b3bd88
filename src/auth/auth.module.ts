import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { GoogleStrategy } from './strategies/google.strategy';
import { OAuthHtmlTemplateService } from './services/oauth-html-template.service';
import { OAuthSecurityService } from './services/oauth-security.service';
import { OAuthErrorHandlerService } from './services/oauth-error-handler.service';
import { OAuthRedirectManagerService } from './services/oauth-redirect-manager.service';
import { FrontendValidationService } from './services/frontend-validation.service';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    GoogleStrategy,
    OAuthHtmlTemplateService,
    OAuthSecurityService,
    OAuthErrorHandlerService,
    OAuthRedirectManagerService,
    FrontendValidationService,
  ],
  exports: [AuthService],
})
export class AuthModule {}
